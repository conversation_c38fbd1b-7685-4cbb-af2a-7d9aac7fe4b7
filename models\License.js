const pool = require("../Schema/databasMySql");

class License {
  static async findByEmail(email) {
    const connection = await pool.getConnection();
    try {
      const [rows] = await connection.execute(
        'SELECT * FROM user_licenses WHERE email = ?',
        [email]
      );
      return rows[0];
    } finally {
      connection.release();
    }
  }

  static async create(email, frontImageUrl, backImageUrl, letters = null, plateNumber = null, carColor = null, carModel = null, status = 'pending') {
    const connection = await pool.getConnection();
    try {
      const [result] = await connection.execute(
        'INSERT INTO user_licenses (email, front_image_url, back_image_url, letters, plate_number, car_color, car_model, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [email, frontImageUrl, backImageUrl, letters, plateNumber, carColor, carModel, status]
      );
      return result.insertId;
    } finally {
      connection.release();
    }
  }

  static async update(email, frontImageUrl, backImageUrl, letters = null, plateNumber = null, carColor = null, carModel = null, status = null) {
    const connection = await pool.getConnection();
    try {
      await connection.execute(
        'UPDATE user_licenses SET front_image_url = ?, back_image_url = ?, letters = ?, plate_number = ?, car_color = ?, car_model = ?, status = ? WHERE email = ?',
        [frontImageUrl, backImageUrl, letters, plateNumber, carColor, carModel, status, email]
      );
    } finally {
      connection.release();
    }
  }

  // دالة لتحديث حالة الترخيص فقط
  static async updateStatus(email, status) {
    const connection = await pool.getConnection();
    try {
      await connection.execute(
        'UPDATE user_licenses SET status = ? WHERE email = ?',
        [status, email]
      );
    } finally {
      connection.release();
    }
  }
}

module.exports = License; 