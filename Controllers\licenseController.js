const License = require('../models/License');

class LicenseController {
  static async getLicense(req, res) {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({ error: 'Email is required' });
      }

      console.log('Looking for license with email:', email);
      const license = await License.findByEmail(email);
      console.log('License found:', license);

      if (!license) {
        return res.status(404).json({ error: 'License data not found' });
      }
      
      const baseUrl = `http://81.10.91.96:${process.env.PORT || 3000}`;
      let frontImageUrl = license.front_image_url;
      let backImageUrl = license.back_image_url;
      
      if ((!frontImageUrl || !backImageUrl) && (license.front_image_path || license.back_image_path)) {
        frontImageUrl = license.front_image_path ? `${baseUrl}/${license.front_image_path.replace(/\\/g, '/')}` : null;
        backImageUrl = license.back_image_path ? `${baseUrl}/${license.back_image_path.replace(/\\/g, '/')}` : null;
      }
      
      const result = {
        id: license.id,
        email: license.email,
        front_image_url: frontImageUrl,
        back_image_url: backImageUrl,
        status: license.status || 'pending',
        upload_date: license.upload_date
      };
      
      res.status(200).json(result);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Error retrieving license data' });
    }
  }

  static async uploadLicense(req, res) {
    try {
      const { email } = req.body;

      if (!email || !req.files.frontImage || !req.files.backImage) {
        return res.status(400).json({ error: 'Email and both images are required' });
      }

      const frontImagePath = req.files.frontImage[0].path;
      const backImagePath = req.files.backImage[0].path;

      const baseUrl = `http://81.10.91.96:${process.env.PORT}`;
      const frontImageUrl = `${baseUrl}/${frontImagePath.replace(/\\/g, '/')}`;
      const backImageUrl = `${baseUrl}/${backImagePath.replace(/\\/g, '/')}`;

      const existingLicense = await License.findByEmail(email);

      if (existingLicense) {
        await License.update(email, frontImageUrl, backImageUrl);
        return res.status(200).json({
          message: 'License updated successfully',
          front_image_url: frontImageUrl,
          back_image_url: backImageUrl
        });
      } else {
        await License.create(email, frontImageUrl, backImageUrl);
        return res.status(201).json({
          message: 'License uploaded successfully',
          front_image_url: frontImageUrl,
          back_image_url: backImageUrl
        });
      }
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Error uploading license' });
    }
  }

  // دالة لتحديث حالة الترخيص
  static async updateLicenseStatus(req, res) {
    try {
      const { email, status } = req.body;

      console.log('Updating license status for:', email, 'to:', status);

      if (!email || !status) {
        return res.status(400).json({ error: 'Email and status are required' });
      }

      // التحقق من أن الحالة صحيحة
      const validStatuses = ['pending', 'approved', 'rejected', 'under_review'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          error: 'Invalid status. Valid statuses are: pending, approved, rejected, under_review'
        });
      }

      const existingLicense = await License.findByEmail(email);
      console.log('Existing license:', existingLicense);

      if (!existingLicense) {
        return res.status(404).json({ error: 'License not found' });
      }

      await License.updateStatus(email, status);
      console.log('Status updated successfully');

      res.status(200).json({
        message: 'License status updated successfully',
        email: email,
        status: status
      });
    } catch (error) {
      console.error('Error updating license status:', error);
      res.status(500).json({
        error: 'Error updating license status',
        details: error.message
      });
    }
  }

  // دالة لإضافة بيانات تجريبية
  static async addTestData(req, res) {
    try {
      const testEmail = '<EMAIL>';
      const frontImageUrl = 'http://81.10.91.96:8132/uploads/1749309350617-3242...';
      const backImageUrl = 'http://81.10.91.96:8132/uploads/1749309350617-2.jp...';

      await License.create(testEmail, frontImageUrl, backImageUrl, 'pending');

      res.status(201).json({
        message: 'Test data added successfully',
        email: testEmail,
        front_image_url: frontImageUrl,
        back_image_url: backImageUrl,
        status: 'pending'
      });
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Error adding test data' });
    }
  }
}

module.exports = LicenseController; 